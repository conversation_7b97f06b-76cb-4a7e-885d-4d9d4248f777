'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { omit } from 'lodash-es';
import { List, Popup, Toast } from 'antd-mobile';
import { Plus, X } from 'lucide-react';
import { Picker } from 'antd-mobile';
import type {
  PickerColumn,
  PickerColumnItem,
  PickerValue,
  PickerValueExtend
} from 'antd-mobile/es/components/picker';
import { CircleCheck, Circle } from 'lucide-react';

import { getClassList } from '@/api/common';

// 后端返回的原始数据结构
interface RawClassData {
  id: string;
  name: string;
  type: number; // 1: 班级，2: 年级或其他层级
  children?: RawClassData[];
}

// 级联选择器项类型
interface CascaderItem extends PickerColumnItem {
  children?: CascaderItem[];
}

// 班级信息
interface ClassInfo {
  id: string;
  name: string;
  gradeName: string;
  fullName: string; // 年级 + 班级的完整名称
}

interface ClassPickerWidgetProps {
  value?: ClassInfo | ClassInfo[];
  onChange?: (value: ClassInfo | ClassInfo[]) => void;
  readOnly?: boolean;
  placeholder?: string;
  multiple?: boolean; // 是否支持多选
}

export default function ClassPickerWidget(props: ClassPickerWidgetProps) {
  const {
    readOnly = false,
    value,
    onChange,
    placeholder = '请选择班级',
    multiple = false
  } = props;

  const [visible, setVisible] = useState(false);
  const [cascaderData, setCascaderData] = useState<CascaderItem[]>([]);
  const [selectedValue, setSelectedValue] = useState<PickerValue[]>([]);
  const [selectedClasses, setSelectedClasses] = useState<ClassInfo[]>([]);

  // 将原始数据转换为级联结构
  const transformCascaderData = useCallback(
    (data: RawClassData[] | undefined): CascaderItem[] => {
      if (!data) return [];

      return data
        .filter((item) => item.type === 2 && item.children?.length) // 筛选有效的年级
        .map((grade) => {
          // 筛选年级下的班级
          const classItems =
            grade.children
              ?.filter((child) => child.type === 1)
              .map((child) => ({
                label: child.name,
                value: child.id
              })) || [];

          // 只有包含班级的年级才返回
          if (classItems.length === 0) return null;

          return {
            label: grade.name,
            value: grade.id,
            children: classItems
          };
        })
        .filter(Boolean) as CascaderItem[]; // 过滤掉空值
    },
    []
  );

  // 获取班级数据
  const fetchData = useCallback(async () => {
    try {
      const res = await getClassList();
      const rawData =
        (
          res as {
            data?: { children?: RawClassData[] };
            children?: RawClassData[];
          }
        )?.data?.children || (res as { children?: RawClassData[] })?.children;

      if (Array.isArray(rawData)) {
        const processedData = transformCascaderData(rawData);
        setCascaderData(processedData);
      } else {
        console.error('获取到的班级数据格式不正确：', res);
        setCascaderData([]);
      }
    } catch (error) {
      console.error('获取班级列表失败：', error);
      setCascaderData([]);
      Toast.show({
        content: '获取班级列表失败',
        position: 'center'
      });
    }
  }, [transformCascaderData]);

  // 根据班级 ID 查找对应的年级和班级信息
  const findClassInfoById = useCallback(
    (classId: string | undefined, data: CascaderItem[]): ClassInfo | null => {
      if (!classId || !data.length) return null;

      for (const grade of data) {
        if (!grade.children?.length) continue;

        const classItem = grade.children.find((cls) => cls.value === classId);
        if (classItem) {
          const gradeLabel = typeof grade.label === 'string' ? grade.label : '';
          const classLabel =
            typeof classItem.label === 'string' ? classItem.label : '';

          return {
            id: classId,
            name: classLabel,
            gradeName: gradeLabel,
            fullName: `${gradeLabel} / ${classLabel}`.trim()
          };
        }
      }
      return null;
    },
    []
  );

  // 根据班级 ID 查找对应的级联选择值
  const findCascadeInfoById = useCallback(
    (classId: string | undefined, data: CascaderItem[]): PickerValue[] => {
      if (!classId || !data.length) return [];

      for (const grade of data) {
        if (!grade.children?.length) continue;

        const classItem = grade.children.find((cls) => cls.value === classId);
        if (classItem) {
          return [grade.value, classItem.value];
        }
      }
      return [];
    },
    []
  );

  // 初始加载数据
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 同步外部 value 变化
  useEffect(() => {
    if (cascaderData.length === 0) return;

    if (multiple) {
      // 多选模式
      const valueArray = Array.isArray(value) ? value : value ? [value] : [];
      const classes = valueArray
        .map((item) => findClassInfoById(item.id, cascaderData))
        .filter(Boolean) as ClassInfo[];
      setSelectedClasses(classes);
    } else {
      // 单选模式
      const singleValue = Array.isArray(value) ? value[0] : value;
      if (singleValue) {
        const classInfo = findClassInfoById(singleValue.id, cascaderData);
        setSelectedClasses(classInfo ? [classInfo] : []);
        setSelectedValue(findCascadeInfoById(singleValue.id, cascaderData));
      } else {
        setSelectedClasses([]);
        setSelectedValue([]);
      }
    }
  }, [value, cascaderData, multiple, findClassInfoById, findCascadeInfoById]);

  // 生成 Picker 的列数据
  const getColumns = useCallback(
    (currentVal: PickerValue[]): PickerColumn[] => {
      const gradeColumn = cascaderData.map((grade) => ({
        label: grade.label,
        value: grade.value
      }));

      const selectedGradeValue = currentVal[0];
      if (!selectedGradeValue) return [gradeColumn];

      const selectedGrade = cascaderData.find(
        (grade) => grade.value === selectedGradeValue
      );
      if (!selectedGrade?.children?.length) return [gradeColumn];

      const classColumn = selectedGrade.children.map((cls) => ({
        label: cls.label,
        value: cls.value
      }));

      return [gradeColumn, classColumn];
    },
    [cascaderData]
  );

  // 只读模式渲染
  if (readOnly) {
    if (!selectedClasses || selectedClasses.length === 0) {
      return <div className="text-gray-400">未选择班级</div>;
    }

    return (
      <div className="flex flex-wrap gap-2">
        {selectedClasses.map((classInfo) => (
          <div
            key={classInfo.id}
            className="inline-flex items-center px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
          >
            {classInfo.fullName}
          </div>
        ))}
      </div>
    );
  }

  // 处理选择器打开
  const handlePickerOpen = () => {
    setVisible(true);
  };

  // 处理选择器关闭
  const handlePickerClose = () => {
    setVisible(false);
  };

  // 移除已选择的班级
  const handleRemoveClass = (classId: string) => {
    const newSelectedClasses = selectedClasses.filter((c) => c.id !== classId);
    setSelectedClasses(newSelectedClasses);

    if (onChange && typeof onChange === 'function') {
      if (multiple) {
        onChange(newSelectedClasses);
      } else {
        if (newSelectedClasses.length > 0) {
          onChange(newSelectedClasses[0]);
        }
      }
    }
  };

  // 处理 Picker 确认事件（单选模式）
  const handleConfirm = (val: PickerValue[], extend: PickerValueExtend) => {
    const classId = val[1]; // 班级 ID
    const classItem = extend.items[1] as PickerColumnItem | undefined;
    const gradeItem = extend.items[0] as PickerColumnItem | undefined;

    setSelectedValue(val);
    setVisible(false);

    // 确保班级 ID 和名称有效，然后调用 onChange
    if (
      onChange &&
      typeof onChange === 'function' &&
      classId &&
      classItem?.label &&
      gradeItem?.label
    ) {
      const gradeLabel =
        typeof gradeItem.label === 'string' ? gradeItem.label : '';
      const classLabel =
        typeof classItem.label === 'string' ? classItem.label : '';

      const classInfo: ClassInfo = {
        id: classId as string,
        name: classLabel,
        gradeName: gradeLabel,
        fullName: `${gradeLabel} / ${classLabel}`.trim()
      };

      if (multiple) {
        // 多选模式：检查是否已存在，不存在则添加
        const exists = selectedClasses.some((c) => c.id === classInfo.id);
        if (!exists) {
          const newClasses = [...selectedClasses, classInfo];
          setSelectedClasses(newClasses);
          onChange(newClasses);
        }
      } else {
        // 单选模式：直接替换
        setSelectedClasses([classInfo]);
        onChange(classInfo);
      }
    }
  };

  // 多选模式的班级列表渲染
  const renderMultipleClassList = () => {
    if (!multiple) return null;

    return (
      <Popup
        visible={visible}
        onMaskClick={handlePickerClose}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '56vh',
          maxHeight: '100vh',
          paddingTop: '10px'
        }}
      >
        <div className="p-4 pt-2">
          <div className="mb-4 flex items-center justify-between text-base">
            <div className="text-stone-400" onClick={handlePickerClose}>
              取消
            </div>
            <div className="text-lg text-stone-900">选择班级</div>
            <div className="text-[#3B82F7]" onClick={handlePickerClose}>
              确定
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mb-4">
            {selectedClasses.map((classInfo) => (
              <div
                key={classInfo.id}
                className="inline-flex items-center px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
              >
                {classInfo.fullName}
                <button
                  type="button"
                  onClick={() => handleRemoveClass(classInfo.id)}
                  className="ml-2 text-blue-500 hover:text-blue-700"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>

          <div className="flex max-h-[50vh] flex-col overflow-y-scroll">
            <List
              style={{
                '--border-top': 'none',
                '--border-inner': 'solid 1px #F1F1F1'
              }}
            >
              {cascaderData.map((grade) => (
                <div key={grade.value}>
                  <div className="px-4 py-2 bg-gray-50 text-sm font-medium text-gray-700">
                    {grade.label}
                  </div>
                  {grade.children?.map((classItem) => {
                    const isSelected = selectedClasses.some(
                      (c) => c.id === classItem.value
                    );
                    const gradeLabel =
                      typeof grade.label === 'string' ? grade.label : '';
                    const classLabel =
                      typeof classItem.label === 'string'
                        ? classItem.label
                        : '';

                    return (
                      <List.Item
                        key={classItem.value}
                        arrow={false}
                        onClick={() => {
                          const classInfo: ClassInfo = {
                            id: classItem.value as string,
                            name: classLabel,
                            gradeName: gradeLabel,
                            fullName: `${gradeLabel} / ${classLabel}`.trim()
                          };

                          if (isSelected) {
                            handleRemoveClass(classInfo.id);
                          } else {
                            const newClasses = [...selectedClasses, classInfo];
                            setSelectedClasses(newClasses);
                            if (onChange && typeof onChange === 'function') {
                              onChange(newClasses);
                            }
                          }
                        }}
                      >
                        <div className="flex flex-1 justify-between items-center">
                          <div>{classItem.label}</div>
                          {isSelected ? (
                            <CircleCheck className="w-5 h-5  text-green-500" />
                          ) : (
                            <Circle className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                      </List.Item>
                    );
                  })}
                </div>
              ))}
            </List>
          </div>
        </div>
      </Popup>
    );
  };

  // 单选模式的渲染
  if (!multiple) {
    const selectedClass = selectedClasses[0];

    return (
      <div className="class-picker-widget">
        <div
          className="w-full min-h-[40px] flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:border-blue-500"
          onClick={handlePickerOpen}
        >
          {selectedClass ? (
            <span className="text-gray-900">{selectedClass.fullName}</span>
          ) : (
            <span className="text-gray-400">{String(placeholder)}</span>
          )}
        </div>

        <Picker
          columns={getColumns}
          visible={visible}
          value={selectedValue}
          onConfirm={handleConfirm}
          onCancel={handlePickerClose}
          onClose={handlePickerClose}
          confirmText="确认"
          cancelText="取消"
          title="选择年级/班级"
        />
      </div>
    );
  }

  // 多选模式的渲染
  return (
    <div className="class-picker-widget">
      <div className="space-y-3">
        <div className="flex flex-wrap gap-2 items-center">
          {selectedClasses.map((classInfo) => (
            <div
              key={classInfo.id}
              className="relative inline-flex items-center px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
            >
              {classInfo.fullName}
              <button
                type="button"
                onClick={() => handleRemoveClass(classInfo.id)}
                className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
              >
                <X className="w-2 h-2" />
              </button>
            </div>
          ))}
          <button
            type="button"
            onClick={handlePickerOpen}
            className="flex items-center text-sm text-primary hover:text-primary/80"
          >
            <div className="w-10 h-10 flex items-center justify-center rounded-full border-2 border-gray-400 overflow-hidden">
              <Plus className="w-4 h-4" />
            </div>
          </button>
        </div>
        {selectedClasses.length === 0 && (
          <div className="text-gray-400 text-sm">{String(placeholder)}</div>
        )}
      </div>

      {/* 多选模式使用自定义弹窗 */}
      {renderMultipleClassList()}
    </div>
  );
}
