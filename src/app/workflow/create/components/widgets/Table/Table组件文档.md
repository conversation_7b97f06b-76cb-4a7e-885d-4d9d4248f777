# Table 表格组件

表格组件用于展示和编辑结构化数据，支持动态添加、编辑和删除行数据。

## 基本用法

```tsx
import TableWidget from './components/widgets/Table';

// 基本使用
<TableWidget
  value={tableData}
  onChange={setTableData}
  schema={{
    properties: {
      columns: [
        { key: 'name', title: '姓名', dataIndex: 'name' },
        { key: 'age', title: '年龄', dataIndex: 'age' },
        { key: 'email', title: '邮箱', dataIndex: 'email' }
      ]
    }
  }}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| value | 表格数据 | `TableRow[]` | `[]` | 否 |
| onChange | 数据变化回调 | `(value: TableRow[]) => void` | - | 否 |
| readOnly | 是否只读模式 | `boolean` | `false` | 否 |
| schema | 表格配置 | `TableSchema` | - | 否 |
| addons | 扩展配置 | `Record<string, unknown>` | - | 否 |

### 类型定义

```typescript
interface TableColumn {
  key: string;           // 列的唯一标识
  title: string;         // 列标题
  dataIndex: string;     // 数据字段名
  width?: number;        // 列宽度
}

interface TableRow {
  [key: string]: any;    // 动态字段
  _id: string;           // 内部唯一标识（自动生成）
}

interface TableSchema {
  properties?: {
    columns?: TableColumn[];  // 列配置
  };
}
```

## 功能特性

### 1. 数据展示
- 支持自定义列配置
- 响应式表格布局
- 空数据状态提示

### 2. 数据编辑
- 添加新行
- 编辑现有行
- 删除行（带确认提示）
- 表单验证（必填字段检查）

### 3. 只读模式
- 纯展示模式，不显示操作按钮
- 保持表格样式一致性

## 使用示例

### 基础表格

```tsx
const [tableData, setTableData] = useState([
  { _id: 'row1', name: '张三', age: '25', email: '<EMAIL>' },
  { _id: 'row2', name: '李四', age: '30', email: '<EMAIL>' }
]);

<TableWidget
  value={tableData}
  onChange={setTableData}
  schema={{
    properties: {
      columns: [
        { key: 'name', title: '姓名', dataIndex: 'name' },
        { key: 'age', title: '年龄', dataIndex: 'age' },
        { key: 'email', title: '邮箱', dataIndex: 'email' }
      ]
    }
  }}
/>
```

### 只读表格

```tsx
<TableWidget
  value={tableData}
  readOnly={true}
  schema={{
    properties: {
      columns: [
        { key: 'name', title: '姓名', dataIndex: 'name' },
        { key: 'age', title: '年龄', dataIndex: 'age' },
        { key: 'email', title: '邮箱', dataIndex: 'email' }
      ]
    }
  }}
/>
```

### 自定义列宽

```tsx
<TableWidget
  value={tableData}
  onChange={setTableData}
  schema={{
    properties: {
      columns: [
        { key: 'name', title: '姓名', dataIndex: 'name', width: 120 },
        { key: 'age', title: '年龄', dataIndex: 'age', width: 80 },
        { key: 'email', title: '邮箱', dataIndex: 'email', width: 200 }
      ]
    }
  }}
/>
```

## 默认配置

如果未提供 `schema.properties.columns`，组件将使用默认配置：

```typescript
const defaultColumns = [
  { key: 'col1', title: '列1', dataIndex: 'col1' },
  { key: 'col2', title: '列2', dataIndex: 'col2' },
  { key: 'col3', title: '列3', dataIndex: 'col3' }
];
```

## 注意事项

1. **唯一标识**: 每行数据必须包含 `_id` 字段作为唯一标识，组件会自动生成
2. **数据验证**: 新增/编辑时会验证所有字段是否为空
3. **删除确认**: 删除操作会弹出确认对话框
4. **响应式**: 表格支持横向滚动，适配移动端
5. **状态管理**: 组件内部管理编辑状态，通过 `onChange` 回调更新外部数据

## 样式说明

组件使用 Tailwind CSS 进行样式设计，主要包括：

- 表格边框和间距
- 悬停效果
- 操作按钮样式
- 响应式布局
- 空状态提示

## 依赖组件

- `antd-mobile`: Button, Input, Dialog, Toast
- `lucide-react`: Plus, Trash2, Edit3 图标
- `lodash-es`: omit 工具函数

## 更新日志

### v1.0.0
- 基础表格功能
- 支持增删改操作
- 只读模式
- 表单验证
- 响应式设计