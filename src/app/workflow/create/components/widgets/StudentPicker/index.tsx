'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { omit } from 'lodash-es';
import { CascadePicker, Toast, Picker } from 'antd-mobile';
import { Plus } from 'lucide-react';
import { getClassList, getStudentListByClass } from '@/api/common';
import StudentAvatar from '@/components/StudentPicker/components/StudentAvatar';

// 类型定义
interface ClassNode {
  id: string;
  name: string;
  type: number;
  children: ClassNode[];
  [key: string]: unknown;
}

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
  classId?: string;
}

interface StudentPickerWidgetProps {
  value?: Student[];
  onChange?: (value: Student[]) => void;
  readOnly?: boolean;
  placeholder?: string;
  schema?: Record<string, unknown>;
  addons?: Record<string, unknown>;
  [key: string]: unknown;
}

// 扁平化处理班级树
function flattenClassTree(data: ClassNode[]): ClassNode[] {
  const result: ClassNode[] = [];

  const processNode = (node: ClassNode) => {
    if (node.children && node.children.length > 0) {
      if (node.type === 2) {
        for (const child of node.children) {
          result.push(child);
        }
      }
      node.children.forEach(processNode);
    }
  };

  data.forEach(processNode);
  return result;
}

// 提取班级数据加载逻辑
function useClassData() {
  const [loading, setLoading] = useState(false);
  const [classData, setClassData] = useState<ClassNode[]>([]);

  useEffect(() => {
    const fetchClassList = async () => {
      setLoading(true);
      try {
        const response = await getClassList();
        const data = response?.data || response;

        if (Array.isArray(data)) {
          setClassData(flattenClassTree(data));
        } else if (data?.children) {
          setClassData(flattenClassTree([data as ClassNode]));
        } else if (data?.list && Array.isArray(data.list)) {
          setClassData(flattenClassTree(data.list));
        } else {
          setClassData([]);
        }
      } catch (error) {
        Toast.show({
          content: '获取班级列表失败',
          position: 'center'
        });
        setClassData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchClassList();
  }, []);

  return { classData, loading };
}

// 提取学生数据加载逻辑
function useStudentData() {
  const [studentMap, setStudentMap] = useState<Record<string, Student[]>>({});
  const [loadedClassIds, setLoadedClassIds] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);

  const fetchStudentList = async (classId: string) => {
    if (studentMap[classId] && loadedClassIds.has(classId)) {
      return;
    }

    try {
      setLoading(true);
      setStudentMap((prev) => ({ ...prev, [classId]: [] }));

      const response = await getStudentListByClass(classId);
      const data = response?.data || response;

      if (data?.list && Array.isArray(data.list)) {
        const students = data.list.map((student: Record<string, unknown>) => ({
          studentId: String(student.id || ''),
          studentName: String(student.name || ''),
          avatar: String(student.avatar || ''),
          gender: Number(student.gender || 0),
          classId
        }));

        setStudentMap((prev) => ({ ...prev, [classId]: students }));
      } else {
        setStudentMap((prev) => ({ ...prev, [classId]: [] }));
      }

      setLoadedClassIds((prev) => {
        const newSet = new Set(prev);
        newSet.add(classId);
        return newSet;
      });
    } catch (error) {
      Toast.show({
        content: '获取学生列表失败',
        position: 'center'
      });
      setStudentMap((prev) => ({ ...prev, [classId]: [] }));
      setLoadedClassIds((prev) => {
        const newSet = new Set(prev);
        newSet.add(classId);
        return newSet;
      });
    } finally {
      setLoading(false);
    }
  };

  return { studentMap, loadedClassIds, fetchStudentList, loading };
}

export default function StudentPickerWidget(props: StudentPickerWidgetProps) {
  const {
    readOnly = false,
    value = [],
    onChange,
    placeholder = '请选择学生',
    ...rest
  } = omit(props, ['addons', 'schema']);

  // 使用提取的自定义 hooks
  const { classData, loading: classLoading } = useClassData();
  const {
    studentMap,
    loadedClassIds,
    fetchStudentList,
    loading: studentLoading
  } = useStudentData();

  const [visible, setVisible] = useState(false);
  const [selectedStudents, setSelectedStudents] = useState<Student[]>(
    value || []
  );

  // 同步外部 value 变化
  useEffect(() => {
    if (value && Array.isArray(value)) {
      setSelectedStudents(value);
    }
  }, [value]);

  // 只读模式渲染
  if (readOnly) {
    if (!selectedStudents || selectedStudents.length === 0) {
      return <div className="text-gray-400">未选择学生</div>;
    }

    return (
      <div className="flex flex-wrap gap-2">
        {selectedStudents.map((student) => (
          <React.Suspense
            key={student.studentId}
            fallback={<div className="w-16 h-20" />}
          >
            <StudentAvatar student={student} size="sm" />
          </React.Suspense>
        ))}
      </div>
    );
  }

  // 选项数据生成
  const options = useMemo(() => {
    return classData.map((classItem) => {
      const students = studentMap[classItem.id] || [];
      const isClassLoaded = loadedClassIds.has(classItem.id);

      return {
        label: classItem.name,
        value: classItem.id,
        children: isClassLoaded
          ? students.length > 0
            ? students.map((student) => ({
                label: student.studentName,
                value: student.studentId,
                student
              }))
            : [{ label: '暂无学生', value: 'no-students', disabled: true }]
          : [{ label: '加载中...', value: 'loading', disabled: true }]
      };
    });
  }, [classData, studentMap, loadedClassIds]);

  // 处理选择器打开
  const handlePickerOpen = () => {
    setVisible(true);
  };

  // 处理级联选择器确认
  const handleConfirm = (val: unknown[]) => {
    if (
      val.length === 2 &&
      val[0] &&
      val[1] &&
      val[1] !== 'loading' &&
      val[1] !== 'no-students'
    ) {
      const classId = String(val[0]);
      const studentId = String(val[1]);

      const selectedClass = classData.find((c) => c.id === classId);
      const selectedStudent = studentMap[classId]?.find(
        (s) => s.studentId === studentId
      );

      if (selectedClass && selectedStudent) {
        const studentExists = selectedStudents.some(
          (s) => s.studentId === selectedStudent.studentId
        );

        if (!studentExists) {
          const newSelectedStudents = [...selectedStudents, selectedStudent];
          setSelectedStudents(newSelectedStudents);
          if (onChange) {
            onChange(newSelectedStudents);
          }
        }
      }
    }
    setVisible(false);
  };

  // 处理选择取消
  const handleCancel = () => setVisible(false);

  // 处理级联选择器值变化
  const handleCascaderChange = (
    val: unknown[],
    extend: Record<string, unknown>
  ) => {
    if (
      val.length > 0 &&
      val[0] &&
      extend?.items &&
      Array.isArray(extend.items) &&
      extend.items.length > 0 &&
      (extend.items[0] as Record<string, unknown>)?.value === val[0]
    ) {
      fetchStudentList(String(val[0]));
    }
  };

  // 移除已选择的学生
  const handleRemoveStudent = (studentId: string) => {
    const newSelectedStudents = selectedStudents.filter(
      (s) => s.studentId !== studentId
    );
    setSelectedStudents(newSelectedStudents);
    if (onChange) {
      onChange(newSelectedStudents);
    }
  };

  return (
    <div className="student-picker-widget">
      <div className="space-y-3">
        <div className="flex flex-wrap gap-2 items-center">
          {selectedStudents.map((student) => (
            <React.Suspense
              key={student.studentId}
              fallback={<div className="w-16 h-20" />}
            >
              <StudentAvatar
                student={student}
                onRemove={handleRemoveStudent}
                size="sm"
              />
            </React.Suspense>
          ))}
          <button
            type="button"
            onClick={handlePickerOpen}
            className="flex items-center text-sm text-primary hover:text-primary/80"
          >
            <div className="w-10 h-10 flex items-center justify-center rounded-full border-2 border-gray-400 overflow-hidden">
              <Plus className="w-4 h-4" />
            </div>
          </button>
        </div>
        {selectedStudents.length === 0 && (
          <div className="text-gray-400 text-sm">{String(placeholder)}</div>
        )}
      </div>

      {/* 级联选择器 */}
      <CascadePicker
        title="选择班级和学生"
        visible={visible}
        options={options}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        onSelect={handleCascaderChange}
      />
    </div>
  );
}
