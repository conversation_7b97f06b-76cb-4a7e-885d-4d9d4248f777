import { Picker } from 'antd-mobile';
import type {
  PickerColumn,
  PickerColumnItem,
  PickerValue,
  PickerValueExtend
} from 'antd-mobile/es/components/picker';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import { getClassList } from '@/api/common';

// 后端返回的原始数据结构
interface RawClassData {
  id: string;
  name: string;
  type: number; // 1: 班级，2: 年级或其他层级
  children?: RawClassData[];
}

// 级联选择器项类型
interface CascaderItem extends PickerColumnItem {
  children?: CascaderItem[];
}

interface ClassPickerProps {
  value?: string; // 选中的班级 ID
  onChange?: (value: string, name: string) => void; // 回调返回班级 ID 和名称
  placeholder?: string;
}

const ClassPicker: React.FC<ClassPickerProps> = ({
  value,
  onChange,
  placeholder = '请选择年级/班级'
}) => {
  console.log('🚀 ~ value:', value);
  const [visible, setVisible] = useState(false);
  const [cascaderData, setCascaderData] = useState<CascaderItem[]>([]);
  const [selectedValue, setSelectedValue] = useState<PickerValue[]>([]);

  // 将原始数据转换为级联结构
  const transformCascaderData = useCallback(
    (data: RawClassData[] | undefined): CascaderItem[] => {
      if (!data) return [];

      return data
        .filter((item) => item.type === 2 && item.children?.length) // 筛选有效的年级
        .map((grade) => {
          // 筛选年级下的班级
          const classItems =
            grade.children
              ?.filter((child) => child.type === 1)
              .map((child) => ({
                label: child.name,
                value: child.id
              })) || [];

          // 只有包含班级的年级才返回
          if (classItems.length === 0) return null;

          return {
            label: grade.name,
            value: grade.id,
            children: classItems
          };
        })
        .filter(Boolean) as CascaderItem[]; // 过滤掉空值
    },
    []
  );

  // 获取班级数据
  const fetchData = useCallback(async () => {
    try {
      const res = await getClassList();
      const rawData = res?.children;

      if (Array.isArray(rawData)) {
        const processedData = transformCascaderData(rawData);
        setCascaderData(processedData);

        // 数据加载完成后立即尝试设置选中值
        if (value) {
          setSelectedValue(findCascadeInfoById(value, processedData));
        }
      } else {
        console.error('获取到的班级数据格式不正确：', res.data);
        setCascaderData([]);
      }
    } catch (error) {
      console.error('获取班级列表失败：', error);
      setCascaderData([]);
    }
  }, [transformCascaderData]);

  // 根据班级 ID 查找对应的年级和班级信息
  const findCascadeInfoById = useCallback(
    (classId: string | undefined, data: CascaderItem[]): PickerValue[] => {
      if (!classId || !data.length) return [];

      for (const grade of data) {
        if (!grade.children?.length) continue;

        const classItem = grade.children.find((cls) => cls.value === classId);
        if (classItem) {
          return [grade.value, classItem.value];
        }
      }
      return [];
    },
    []
  );

  // 计算当前选中的名称
  const selectedName = useMemo(() => {
    if (
      !selectedValue.length ||
      selectedValue.length < 2 ||
      !cascaderData.length
    ) {
      return undefined;
    }

    const [gradeId, classId] = selectedValue;
    const grade = cascaderData.find((g) => g.value === gradeId);
    if (!grade) return undefined;

    const classItem = grade.children?.find((c) => c.value === classId);
    if (!classItem) return undefined;

    const gradeLabel = typeof grade.label === 'string' ? grade.label : '';
    const classLabel =
      typeof classItem.label === 'string' ? classItem.label : '';

    return `${gradeLabel} / ${classLabel}`.trim();
  }, [selectedValue, cascaderData]);

  // 初始加载数据
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 当外部 value 或 cascaderData 变化时，更新内部选中状态
  useEffect(() => {
    if (value && cascaderData.length > 0) {
      setSelectedValue(findCascadeInfoById(value, cascaderData));
    } else {
      setSelectedValue([]);
    }
  }, [value, cascaderData, findCascadeInfoById]);

  // 生成 Picker 的列数据
  const getColumns = useCallback(
    (currentVal: PickerValue[]): PickerColumn[] => {
      const gradeColumn = cascaderData.map((grade) => ({
        label: grade.label,
        value: grade.value
      }));

      const selectedGradeValue = currentVal[0];
      if (!selectedGradeValue) return [gradeColumn];

      const selectedGrade = cascaderData.find(
        (grade) => grade.value === selectedGradeValue
      );
      if (!selectedGrade?.children?.length) return [gradeColumn];

      const classColumn = selectedGrade.children.map((cls) => ({
        label: cls.label,
        value: cls.value
      }));

      return [gradeColumn, classColumn];
    },
    [cascaderData]
  );

  const handleClick = () => {
    console.log('🚀 ~ handleClick ~ value:', value);
    setVisible(true);
  };

  // 处理 Picker 确认事件
  const handleConfirm = (val: PickerValue[], extend: PickerValueExtend) => {
    const classId = val[1]; // 班级 ID
    const classItem = extend.items[1] as PickerColumnItem | undefined;

    setSelectedValue(val);
    setVisible(false);

    // 确保班级 ID 和名称有效，然后调用 onChange
    if (onChange && classId && classItem?.label) {
      const classLabel =
        typeof classItem.label === 'string' ? classItem.label : '';
      onChange(classId as string, classLabel);
    }
  };

  return (
    <div className="w-full">
      <div className="w-full " onClick={handleClick}>
        {selectedName ? (
          <span>{selectedName}</span>
        ) : (
          <span>{placeholder}</span>
        )}
      </div>
      <Picker
        columns={getColumns}
        visible={visible}
        value={selectedValue}
        onConfirm={handleConfirm}
        onCancel={() => setVisible(false)}
        onClose={() => setVisible(false)}
        confirmText="确认"
        cancelText="取消"
        title="选择年级/班级"
      />
    </div>
  );
};

export default memo(ClassPicker);
